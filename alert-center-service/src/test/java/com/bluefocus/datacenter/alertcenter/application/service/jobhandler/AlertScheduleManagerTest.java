package com.bluefocus.datacenter.alertcenter.application.service.jobhandler;

import com.bluefocus.datacenter.alertcenter.domain.alert.entity.AlertRule;
import com.bluefocus.datacenter.alertcenter.domain.alert.service.AlertRuleDomainService;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * AlertScheduleManager测试类
 *
 * <AUTHOR>
 * @since 2025/5/26
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class AlertScheduleManagerTest {

    @Resource
    private AlertScheduleManager alertScheduleManager;

    @Resource
    private AlertRuleDomainService alertRuleDomainService;

    @Test
    public void testExecute_AutoMode_Morning() {
        // 测试自动模式 - 上午时间
        LocalDateTime morningTime = LocalDateTime.now().withHour(8).withMinute(0);
        String param = "mode=AUTO;time=" + morningTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        
        log.info("测试上午自动执行，参数: {}", param);
        ReturnT<String> result = alertScheduleManager.execute(param);
        
        assert ReturnT.SUCCESS_CODE == result.getCode();
        log.info("上午自动执行测试结果: {}", result);
    }

    @Test
    public void testExecute_AutoMode_Afternoon() {
        // 测试自动模式 - 下午时间
        LocalDateTime afternoonTime = LocalDateTime.now().withHour(14).withMinute(0);
        String param = "mode=AUTO;time=" + afternoonTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        
        log.info("测试下午自动执行，参数: {}", param);
        ReturnT<String> result = alertScheduleManager.execute(param);
        
        assert ReturnT.SUCCESS_CODE == result.getCode();
        log.info("下午自动执行测试结果: {}", result);
    }

    @Test
    public void testExecute_AutoMode_DailyReport() {
        // 测试自动模式 - 日报时间
        LocalDateTime reportTime = LocalDateTime.now().withHour(9).withMinute(30);
        String param = "mode=AUTO;time=" + reportTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        
        log.info("测试日报自动执行，参数: {}", param);
        ReturnT<String> result = alertScheduleManager.execute(param);
        
        assert ReturnT.SUCCESS_CODE == result.getCode();
        log.info("日报自动执行测试结果: {}", result);
    }

    @Test
    public void testExecute_ManualMode() {
        // 测试手动模式 - 执行所有配置
        String param = "mode=MANUAL";
        
        log.info("测试手动执行所有配置，参数: {}", param);
        ReturnT<String> result = alertScheduleManager.execute(param);
        
        assert ReturnT.SUCCESS_CODE == result.getCode();
        log.info("手动执行测试结果: {}", result);
    }

    @Test
    public void testExecute_SpecificRules() {
        // 测试执行特定规则
        String param = "mode=MANUAL;rules=单条告警,整体告警";
        
        log.info("测试执行特定规则，参数: {}", param);
        ReturnT<String> result = alertScheduleManager.execute(param);
        
        assert ReturnT.SUCCESS_CODE == result.getCode();
        log.info("特定规则执行测试结果: {}", result);
    }

    @Test
    public void testExecute_ParallelExecution() {
        // 测试并行执行
        String param = "mode=MANUAL;parallel=true";
        
        log.info("测试并行执行，参数: {}", param);
        ReturnT<String> result = alertScheduleManager.execute(param);
        
        assert ReturnT.SUCCESS_CODE == result.getCode();
        log.info("并行执行测试结果: {}", result);
    }

    @Test
    public void testExecute_DefaultParam() {
        // 测试默认参数（空参数）
        log.info("测试默认参数执行");
        ReturnT<String> result = alertScheduleManager.execute("");
        
        assert ReturnT.SUCCESS_CODE == result.getCode();
        log.info("默认参数执行测试结果: {}", result);
    }

    @Test
    public void testExecute_NullParam() {
        // 测试null参数
        log.info("测试null参数执行");
        ReturnT<String> result = alertScheduleManager.execute(null);
        
        assert ReturnT.SUCCESS_CODE == result.getCode();
        log.info("null参数执行测试结果: {}", result);
    }

    @Test
    public void testExecute_InvalidParam() {
        // 测试无效参数
        String param = "invalid=param;format";
        
        log.info("测试无效参数，参数: {}", param);
        ReturnT<String> result = alertScheduleManager.execute(param);
        
        // 无效参数应该使用默认配置，仍然成功
        assert ReturnT.SUCCESS_CODE == result.getCode();
        log.info("无效参数执行测试结果: {}", result);
    }

    @Test
    public void testExecute_NonExistentRule() {
        // 测试不存在的规则
        String param = "mode=MANUAL;rules=不存在的规则";
        
        log.info("测试不存在的规则，参数: {}", param);
        ReturnT<String> result = alertScheduleManager.execute(param);
        
        // 不存在的规则应该被忽略，返回成功
        assert ReturnT.SUCCESS_CODE == result.getCode();
        log.info("不存在规则执行测试结果: {}", result);
    }

    @Test
    public void testTimeSlotCoverage() {
        // 测试不同时间段的覆盖
        int[] testHours = {6, 8, 10, 12, 14, 16, 18, 20, 22, 2}; // 包含各个时间段
        
        for (int hour : testHours) {
            LocalDateTime testTime = LocalDateTime.now().withHour(hour).withMinute(0);
            String param = "mode=AUTO;time=" + testTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            
            log.info("测试时间段 {}:00，参数: {}", hour, param);
            ReturnT<String> result = alertScheduleManager.execute(param);
            
            assert ReturnT.SUCCESS_CODE == result.getCode();
            log.info("时间段 {}:00 执行结果: {}", hour, result.getMsg());
        }
    }

    @Test
    public void testConfigurationValidation() {
        // 验证配置的完整性
        log.info("=== 配置验证测试 ===");
        
        List<AlertRule> rules = alertRuleDomainService.list();
        log.info("总规则数: {}", rules.size());
        
        int totalExecutions = 0;
        int morningExecutions = 0;
        int afternoonExecutions = 0;
        int dailyExecutions = 0;
        
        for (AlertRule rule : rules) {
            if (rule.getExecutions() == null) continue;
            
            log.info("规则: {} - 执行配置数: {}", rule.getName(), rule.getExecutions().size());
            
            for (AlertRule.AlertExecution execution : rule.getExecutions()) {
                totalExecutions++;
                String remark = execution.getRemark();
                
                if (remark != null) {
                    if (remark.contains("上午") || remark.contains("前一天")) {
                        morningExecutions++;
                    } else if (remark.contains("下午") || remark.contains("当天")) {
                        afternoonExecutions++;
                    } else if (remark.contains("日报") || remark.contains("每日")) {
                        dailyExecutions++;
                    }
                }
                
                log.info("  执行配置: {} - {}", execution.getRemark(), execution.getExecKey());
            }
        }
        
        log.info("配置统计:");
        log.info("  总执行配置数: {}", totalExecutions);
        log.info("  上午执行配置: {}", morningExecutions);
        log.info("  下午执行配置: {}", afternoonExecutions);
        log.info("  日报执行配置: {}", dailyExecutions);
        
        // 基本验证
        assert totalExecutions > 0 : "应该有执行配置";
        assert morningExecutions > 0 : "应该有上午执行配置";
        assert afternoonExecutions > 0 : "应该有下午执行配置";
    }

    @Test
    public void testScheduleManagerIntegration() {
        // 集成测试：验证调度管理器与其他组件的集成
        log.info("=== 调度管理器集成测试 ===");
        
        // 测试不同的参数组合
        String[] testParams = {
            "",                                    // 默认参数
            "mode=AUTO",                          // 仅指定模式
            "mode=MANUAL",                        // 手动模式
            "mode=AUTO;parallel=false",           // 串行执行
            "mode=MANUAL;rules=单条告警",          // 特定规则
        };
        
        for (String param : testParams) {
            log.info("测试参数组合: '{}'", param);
            
            long startTime = System.currentTimeMillis();
            ReturnT<String> result = alertScheduleManager.execute(param);
            long endTime = System.currentTimeMillis();
            
            assert ReturnT.SUCCESS_CODE == result.getCode();
            log.info("参数 '{}' 执行结果: {}, 耗时: {}ms", 
                param, result.getMsg(), endTime - startTime);
        }
    }

    @Test
    public void testErrorHandling() {
        // 测试错误处理
        log.info("=== 错误处理测试 ===");
        
        // 测试各种可能导致错误的参数
        String[] errorParams = {
            "mode=INVALID",                       // 无效模式
            "time=invalid-time-format",           // 无效时间格式
            "mode=AUTO;time=2025-13-45T25:70:80", // 无效日期时间
        };
        
        for (String param : errorParams) {
            log.info("测试错误参数: '{}'", param);
            
            try {
                ReturnT<String> result = alertScheduleManager.execute(param);
                // 错误参数应该被优雅处理，使用默认值
                log.info("错误参数 '{}' 处理结果: {}", param, result.getMsg());
            } catch (Exception e) {
                log.info("错误参数 '{}' 抛出异常: {}", param, e.getMessage());
            }
        }
    }
}
