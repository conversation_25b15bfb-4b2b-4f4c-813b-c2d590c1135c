package com.bluefocus.datacenter.alertcenter.application.service.jobhandler;

import com.bluefocus.datacenter.alertcenter.application.service.alert.AlertExecParam;
import com.bluefocus.datacenter.alertcenter.application.service.alert.Alerter;
import com.bluefocus.datacenter.alertcenter.domain.alert.entity.AlertRule;
import com.bluefocus.datacenter.alertcenter.domain.alert.service.AlertRuleDomainService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 统一告警任务处理器
 * 使用统一配置文件，支持一个规则多个执行配置的场景
 *
 * <AUTHOR>
 * @since 2025/5/26
 */
@JobHandler("unifiedAlerterJobHandler")
@Component
@RequiredArgsConstructor
@Slf4j
public class UnifiedAlerterJobHandler extends IJobHandler {
    private final AlertRuleDomainService alertRuleDomainService;
    private final Alerter alerter;

    @Override
    public ReturnT<String> execute(String param) {
        log.info("开始执行统一告警任务，参数: {}", param);

        try {
            // 参数格式: ruleName:execIndex 或者 ruleName (默认执行第一个)
            String[] parts = param.split(":");
            String ruleName = parts[0].trim();
            int execIndex = parts.length > 1 ? Integer.parseInt(parts[1].trim()) : 0;

            if (StringUtils.isBlank(ruleName)) {
                log.warn("规则名称不能为空");
                return new ReturnT<>(ReturnT.FAIL_CODE, "规则名称不能为空");
            }

            AlertRule alertRule = alertRuleDomainService.findByName(ruleName);
            if (alertRule == null) {
                log.warn("未找到告警规则: {}", ruleName);
                return new ReturnT<>(ReturnT.FAIL_CODE, "未找到告警规则: " + ruleName);
            }

            if (alertRule.getExecutions() == null || alertRule.getExecutions().isEmpty()) {
                log.warn("告警规则 {} 没有配置执行参数", ruleName);
                return new ReturnT<>(ReturnT.FAIL_CODE, "告警规则没有配置执行参数");
            }

            if (execIndex >= alertRule.getExecutions().size()) {
                log.warn("执行配置索引 {} 超出范围，规则 {} 只有 {} 个执行配置",
                        execIndex, ruleName, alertRule.getExecutions().size());
                return new ReturnT<>(ReturnT.FAIL_CODE, "执行配置索引超出范围");
            }

            AlertRule.AlertExecution execution = alertRule.getExecutions().get(execIndex);
            String execKey = execution.getExecKey();

            if (StringUtils.isBlank(execKey)) {
                log.warn("执行配置的execKey不能为空");
                return new ReturnT<>(ReturnT.FAIL_CODE, "执行配置的execKey不能为空");
            }

            AlertExecParam execParam = new AlertExecParam(execKey);

            // 添加执行参数
            if (execution.getParams() != null) {
                for (Map.Entry<String, String> entry : execution.getParams().entrySet()) {
                    if (entry.getValue() != null) {
                        execParam.addParam(entry.getKey(), entry.getValue());
                    }
                }
            }

            log.info("开始执行告警，规则: {}, 执行配置索引: {}, 备注: {}, 参数: {}",
                    ruleName, execIndex, execution.getRemark(), execParam);

            alerter.alert(alertRule, execParam);

            log.info("告警执行成功，规则: {}, 执行配置: {}", ruleName, execution.getRemark());
            return ReturnT.SUCCESS;

        } catch (NumberFormatException e) {
            log.error("执行配置索引格式错误，参数: {}, 异常: {}", param, e.getMessage());
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行配置索引格式错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("执行统一告警任务失败，参数: {}, 异常: {}", param, e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "Job execution failed: " + e.getMessage());
        }
    }
}
