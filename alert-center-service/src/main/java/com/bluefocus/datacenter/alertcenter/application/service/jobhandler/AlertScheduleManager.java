package com.bluefocus.datacenter.alertcenter.application.service.jobhandler;

import com.bluefocus.datacenter.alertcenter.application.service.alert.AlertExecParam;
import com.bluefocus.datacenter.alertcenter.application.service.alert.Alerter;
import com.bluefocus.datacenter.alertcenter.domain.alert.entity.AlertRule;
import com.bluefocus.datacenter.alertcenter.domain.alert.service.AlertRuleDomainService;
import com.bluefocus.datacenter.alertcenter.infrastructure.util.FreemarkerRender;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 告警调度管理器
 * 通过单个XXL-Job任务管理所有告警规则的执行
 *
 * <AUTHOR>
 * @since 2025/5/26
 */
@JobHandler("alertScheduleManager")
@Component
@RequiredArgsConstructor
@Slf4j
public class AlertScheduleManager extends IJobHandler {
    
    private final AlertRuleDomainService alertRuleDomainService;
    private final Alerter alerter;
    private final FreemarkerRender freemarkerRender;
    
    // 线程池用于并发执行告警
    private final ExecutorService executorService = Executors.newFixedThreadPool(5);

    @Override
    public ReturnT<String> execute(String param) {
        log.info("开始执行告警调度管理，参数: {}", param);
        
        try {
            // 解析调度参数
            ScheduleConfig config = parseScheduleConfig(param);
            log.info("调度配置: {}", config);
            
            // 获取所有告警规则
            List<AlertRule> allRules = alertRuleDomainService.list();
            log.info("加载告警规则数量: {}", allRules.size());
            
            // 筛选需要执行的规则
            List<ExecutionTask> tasksToExecute = selectTasksToExecute(allRules, config);
            log.info("本次需要执行的任务数量: {}", tasksToExecute.size());
            
            if (tasksToExecute.isEmpty()) {
                log.info("当前时间段没有需要执行的告警任务");
                return ReturnT.SUCCESS;
            }
            
            // 执行告警任务
            ExecutionResult result = executeTasks(tasksToExecute);
            
            // 返回执行结果
            String resultMsg = String.format("执行完成 - 成功: %d, 失败: %d, 总计: %d", 
                result.getSuccessCount(), result.getFailureCount(), result.getTotalCount());
            
            log.info(resultMsg);
            
            if (result.getFailureCount() > 0) {
                log.warn("部分任务执行失败，失败详情: {}", result.getFailureDetails());
                return new ReturnT<>(ReturnT.SUCCESS_CODE, resultMsg + ", 部分失败: " + result.getFailureDetails());
            }
            
            return new ReturnT<>(ReturnT.SUCCESS_CODE, resultMsg);
            
        } catch (Exception e) {
            log.error("告警调度管理执行失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "调度执行失败: " + e.getMessage());
        }
    }

    /**
     * 解析调度配置参数
     */
    private ScheduleConfig parseScheduleConfig(String param) {
        ScheduleConfig config = new ScheduleConfig();
        
        if (param == null || param.trim().isEmpty()) {
            // 使用默认配置
            config.setExecutionMode(ExecutionMode.AUTO);
            config.setCurrentTime(LocalDateTime.now());
            return config;
        }
        
        // 解析参数格式: mode=AUTO|MANUAL;time=2025-01-01T10:00:00;rules=rule1,rule2
        String[] parts = param.split(";");
        for (String part : parts) {
            String[] kv = part.split("=", 2);
            if (kv.length != 2) continue;
            
            String key = kv[0].trim();
            String value = kv[1].trim();
            
            switch (key.toLowerCase()) {
                case "mode":
                    config.setExecutionMode(ExecutionMode.valueOf(value.toUpperCase()));
                    break;
                case "time":
                    config.setCurrentTime(LocalDateTime.parse(value));
                    break;
                case "rules":
                    config.setSpecificRules(Arrays.asList(value.split(",")));
                    break;
                case "parallel":
                    config.setParallelExecution(Boolean.parseBoolean(value));
                    break;
            }
        }
        
        if (config.getCurrentTime() == null) {
            config.setCurrentTime(LocalDateTime.now());
        }
        
        return config;
    }

    /**
     * 筛选需要执行的任务
     */
    private List<ExecutionTask> selectTasksToExecute(List<AlertRule> allRules, ScheduleConfig config) {
        List<ExecutionTask> tasks = new ArrayList<>();
        
        for (AlertRule rule : allRules) {
            if (rule.getExecutions() == null || rule.getExecutions().isEmpty()) {
                log.debug("规则 {} 没有执行配置，跳过", rule.getName());
                continue;
            }
            
            // 如果指定了特定规则，只执行指定的规则
            if (config.getSpecificRules() != null && !config.getSpecificRules().isEmpty()) {
                if (!config.getSpecificRules().contains(rule.getName())) {
                    continue;
                }
            }
            
            for (int i = 0; i < rule.getExecutions().size(); i++) {
                AlertRule.AlertExecution execution = rule.getExecutions().get(i);
                
                // 根据执行模式判断是否需要执行
                if (shouldExecute(rule, execution, config)) {
                    ExecutionTask task = new ExecutionTask();
                    task.setRule(rule);
                    task.setExecution(execution);
                    task.setExecutionIndex(i);
                    task.setScheduledTime(config.getCurrentTime());
                    
                    tasks.add(task);
                    log.info("添加执行任务: {} [{}] - {}", 
                        rule.getName(), i, execution.getRemark());
                }
            }
        }
        
        return tasks;
    }

    /**
     * 判断是否应该执行某个告警配置
     */
    private boolean shouldExecute(AlertRule rule, AlertRule.AlertExecution execution, ScheduleConfig config) {
        if (config.getExecutionMode() == ExecutionMode.MANUAL) {
            // 手动模式：执行所有配置
            return true;
        }

        // 自动模式：根据时间和配置判断
        LocalDateTime currentTime = config.getCurrentTime();
        int currentHour = currentTime.getHour();

        // 根据执行配置的备注判断执行时间
        String remark = execution.getRemark();
        if (remark == null) {
            return true; // 没有备注的默认执行
        }

        String remarkLower = remark.toLowerCase();

        // 上午执行的任务（6:00-12:00）
        if (remarkLower.contains("上午") || remarkLower.contains("am") || remarkLower.contains("前一天")) {
            return currentHour >= 6 && currentHour < 12;
        }

        // 下午执行的任务（12:00-18:00）
        if (remarkLower.contains("下午") || remarkLower.contains("pm") || remarkLower.contains("当天")) {
            return currentHour >= 12 && currentHour < 18;
        }

        // 每日执行的任务（如飞书日报）
        if (remarkLower.contains("每日") || remarkLower.contains("日报")) {
            return currentHour >= 9 && currentHour < 10; // 每天9点执行
        }

        // 默认执行
        return true;
    }

    /**
     * 执行告警任务
     */
    private ExecutionResult executeTasks(List<ExecutionTask> tasks) {
        ExecutionResult result = new ExecutionResult();
        result.setTotalCount(tasks.size());
        
        if (tasks.size() == 1 || !isParallelExecutionEnabled()) {
            // 串行执行
            executeTasksSequentially(tasks, result);
        } else {
            // 并行执行
            executeTasksInParallel(tasks, result);
        }
        
        return result;
    }

    /**
     * 串行执行任务
     */
    private void executeTasksSequentially(List<ExecutionTask> tasks, ExecutionResult result) {
        for (ExecutionTask task : tasks) {
            try {
                executeTask(task);
                result.incrementSuccess();
                log.info("任务执行成功: {} [{}]", task.getRule().getName(), task.getExecutionIndex());
            } catch (Exception e) {
                result.incrementFailure();
                String errorMsg = String.format("%s[%d]: %s", 
                    task.getRule().getName(), task.getExecutionIndex(), e.getMessage());
                result.addFailureDetail(errorMsg);
                log.error("任务执行失败: {}", errorMsg, e);
            }
        }
    }

    /**
     * 并行执行任务
     */
    private void executeTasksInParallel(List<ExecutionTask> tasks, ExecutionResult result) {
        List<CompletableFuture<Void>> futures = tasks.stream()
            .map(task -> CompletableFuture.runAsync(() -> {
                try {
                    executeTask(task);
                    result.incrementSuccess();
                    log.info("任务执行成功: {} [{}]", task.getRule().getName(), task.getExecutionIndex());
                } catch (Exception e) {
                    result.incrementFailure();
                    String errorMsg = String.format("%s[%d]: %s", 
                        task.getRule().getName(), task.getExecutionIndex(), e.getMessage());
                    result.addFailureDetail(errorMsg);
                    log.error("任务执行失败: {}", errorMsg, e);
                }
            }, executorService))
            .collect(Collectors.toList());
        
        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    /**
     * 执行单个任务
     */
    private void executeTask(ExecutionTask task) {
        AlertRule rule = task.getRule();
        AlertRule.AlertExecution execution = task.getExecution();
        
        // 构建执行参数
        AlertExecParam execParam = new AlertExecParam(execution.getExecKey());
        
        if (execution.getParams() != null) {
            for (Map.Entry<String, String> entry : execution.getParams().entrySet()) {
                if (entry.getValue() != null) {
                    execParam.addParam(entry.getKey(), entry.getValue());
                }
            }
        }
        
        // 执行告警
        alerter.alert(rule, execParam);
    }

    /**
     * 检查是否启用并行执行
     */
    private boolean isParallelExecutionEnabled() {
        // 可以从配置文件读取，这里简单返回true
        return true;
    }

    // 内部类定义
    
    /**
     * 调度配置
     */
    public static class ScheduleConfig {
        private ExecutionMode executionMode = ExecutionMode.AUTO;
        private LocalDateTime currentTime;
        private List<String> specificRules;
        private boolean parallelExecution = true;
        
        // getters and setters
        public ExecutionMode getExecutionMode() { return executionMode; }
        public void setExecutionMode(ExecutionMode executionMode) { this.executionMode = executionMode; }
        public LocalDateTime getCurrentTime() { return currentTime; }
        public void setCurrentTime(LocalDateTime currentTime) { this.currentTime = currentTime; }
        public List<String> getSpecificRules() { return specificRules; }
        public void setSpecificRules(List<String> specificRules) { this.specificRules = specificRules; }
        public boolean isParallelExecution() { return parallelExecution; }
        public void setParallelExecution(boolean parallelExecution) { this.parallelExecution = parallelExecution; }
        
        @Override
        public String toString() {
            return String.format("ScheduleConfig{mode=%s, time=%s, rules=%s, parallel=%s}", 
                executionMode, currentTime, specificRules, parallelExecution);
        }
    }

    /**
     * 执行模式
     */
    public enum ExecutionMode {
        AUTO,   // 自动模式：根据时间判断
        MANUAL  // 手动模式：执行所有配置
    }

    /**
     * 执行任务
     */
    public static class ExecutionTask {
        private AlertRule rule;
        private AlertRule.AlertExecution execution;
        private int executionIndex;
        private LocalDateTime scheduledTime;
        
        // getters and setters
        public AlertRule getRule() { return rule; }
        public void setRule(AlertRule rule) { this.rule = rule; }
        public AlertRule.AlertExecution getExecution() { return execution; }
        public void setExecution(AlertRule.AlertExecution execution) { this.execution = execution; }
        public int getExecutionIndex() { return executionIndex; }
        public void setExecutionIndex(int executionIndex) { this.executionIndex = executionIndex; }
        public LocalDateTime getScheduledTime() { return scheduledTime; }
        public void setScheduledTime(LocalDateTime scheduledTime) { this.scheduledTime = scheduledTime; }
    }

    /**
     * 执行结果
     */
    public static class ExecutionResult {
        private int totalCount;
        private int successCount;
        private int failureCount;
        private List<String> failureDetails = new ArrayList<>();
        
        public synchronized void incrementSuccess() { successCount++; }
        public synchronized void incrementFailure() { failureCount++; }
        public synchronized void addFailureDetail(String detail) { failureDetails.add(detail); }
        
        // getters and setters
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        public int getSuccessCount() { return successCount; }
        public int getFailureCount() { return failureCount; }
        public List<String> getFailureDetails() { return failureDetails; }
    }
}
