package com.bluefocus.datacenter.alertcenter.application.service.schedule;

import com.bluefocus.datacenter.alertcenter.domain.alert.entity.AlertRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Predicate;

/**
 * 告警调度策略
 * 定义不同类型告警的执行时间策略
 *
 * <AUTHOR>
 * @since 2025/5/26
 */
@Component
@Slf4j
public class AlertScheduleStrategy {

    /**
     * 时间段定义
     */
    public static class TimeSlot {
        public static final TimeSlot MORNING = new TimeSlot(6, 12, "上午时段");
        public static final TimeSlot AFTERNOON = new TimeSlot(12, 18, "下午时段");
        public static final TimeSlot DAILY_REPORT = new TimeSlot(9, 10, "日报时段");
        
        private final int startHour;
        private final int endHour;
        private final String description;
        
        public TimeSlot(int startHour, int endHour, String description) {
            this.startHour = startHour;
            this.endHour = endHour;
            this.description = description;
        }
        
        public boolean contains(LocalDateTime time) {
            int hour = time.getHour();
            return hour >= startHour && hour < endHour;
        }
        
        public String getDescription() { return description; }
        public int getStartHour() { return startHour; }
        public int getEndHour() { return endHour; }
    }

    /**
     * 调度规则定义
     */
    private final Map<String, ScheduleRule> scheduleRules = new HashMap<>();

    public AlertScheduleStrategy() {
        initializeScheduleRules();
    }

    /**
     * 初始化调度规则
     */
    private void initializeScheduleRules() {
        // 上午执行规则
        scheduleRules.put("morning", new ScheduleRule(
            "上午执行",
            execution -> {
                String remark = execution.getRemark();
                return remark != null && (
                    remark.contains("上午") || 
                    remark.contains("前一天") || 
                    remark.contains("am")
                );
            },
            TimeSlot.MORNING
        ));

        // 下午执行规则
        scheduleRules.put("afternoon", new ScheduleRule(
            "下午执行",
            execution -> {
                String remark = execution.getRemark();
                return remark != null && (
                    remark.contains("下午") || 
                    remark.contains("当天") || 
                    remark.contains("pm")
                );
            },
            TimeSlot.AFTERNOON
        ));

        // 日报执行规则
        scheduleRules.put("daily", new ScheduleRule(
            "每日报告",
            execution -> {
                String remark = execution.getRemark();
                return remark != null && (
                    remark.contains("日报") || 
                    remark.contains("每日")
                );
            },
            TimeSlot.DAILY_REPORT
        ));
    }

    /**
     * 判断执行配置是否应该在当前时间执行
     */
    public boolean shouldExecute(AlertRule.AlertExecution execution, LocalDateTime currentTime) {
        for (ScheduleRule rule : scheduleRules.values()) {
            if (rule.matches(execution)) {
                boolean inTimeSlot = rule.getTimeSlot().contains(currentTime);
                log.debug("执行配置 '{}' 匹配规则 '{}', 当前时间 {} 在时间段内: {}", 
                    execution.getRemark(), rule.getName(), currentTime, inTimeSlot);
                return inTimeSlot;
            }
        }
        
        // 如果没有匹配的规则，默认在工作时间执行（9:00-18:00）
        int hour = currentTime.getHour();
        boolean defaultExecute = hour >= 9 && hour < 18;
        log.debug("执行配置 '{}' 未匹配任何规则，使用默认策略，当前时间 {} 可执行: {}", 
            execution.getRemark(), currentTime, defaultExecute);
        return defaultExecute;
    }

    /**
     * 获取执行配置的调度信息
     */
    public ScheduleInfo getScheduleInfo(AlertRule.AlertExecution execution) {
        for (ScheduleRule rule : scheduleRules.values()) {
            if (rule.matches(execution)) {
                return new ScheduleInfo(rule.getName(), rule.getTimeSlot(), true);
            }
        }
        
        // 默认调度信息
        return new ScheduleInfo("默认策略", new TimeSlot(9, 18, "工作时间"), false);
    }

    /**
     * 调度规则内部类
     */
    private static class ScheduleRule {
        private final String name;
        private final Predicate<AlertRule.AlertExecution> matcher;
        private final TimeSlot timeSlot;
        
        public ScheduleRule(String name, Predicate<AlertRule.AlertExecution> matcher, TimeSlot timeSlot) {
            this.name = name;
            this.matcher = matcher;
            this.timeSlot = timeSlot;
        }
        
        public boolean matches(AlertRule.AlertExecution execution) {
            return matcher.test(execution);
        }
        
        public String getName() { return name; }
        public TimeSlot getTimeSlot() { return timeSlot; }
    }

    /**
     * 调度信息
     */
    public static class ScheduleInfo {
        private final String ruleName;
        private final TimeSlot timeSlot;
        private final boolean hasSpecificRule;
        
        public ScheduleInfo(String ruleName, TimeSlot timeSlot, boolean hasSpecificRule) {
            this.ruleName = ruleName;
            this.timeSlot = timeSlot;
            this.hasSpecificRule = hasSpecificRule;
        }
        
        public String getRuleName() { return ruleName; }
        public TimeSlot getTimeSlot() { return timeSlot; }
        public boolean hasSpecificRule() { return hasSpecificRule; }
        
        @Override
        public String toString() {
            return String.format("ScheduleInfo{rule='%s', timeSlot='%s', specific=%s}", 
                ruleName, timeSlot.getDescription(), hasSpecificRule);
        }
    }
}
