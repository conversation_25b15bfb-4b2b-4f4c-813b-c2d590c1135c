# 单任务管理所有告警规则方案

## 🎯 方案概述

通过一个XXL-Job任务来管理所有告警规则的执行，实现统一调度、智能时间判断和灵活配置管理。

## 🏗️ 架构设计

### 核心组件

1. **AlertScheduleManager** - 调度管理器
   - 统一入口，管理所有告警规则
   - 智能时间判断，自动选择执行配置
   - 支持并行/串行执行模式

2. **ScheduleConfig** - 调度配置
   - 执行模式（自动/手动）
   - 时间参数
   - 规则筛选
   - 并行控制

3. **时间策略** - 智能调度
   - 上午时段：6:00-12:00（前一天数据）
   - 下午时段：12:00-18:00（当天数据）
   - 日报时段：9:00-10:00（日报推送）

## 📋 使用方式

### XXL-Job配置

只需要配置**一个**XXL-Job任务：

```
任务名称: 告警统一调度
JobHandler: alertScheduleManager
调度类型: CRON
Cron表达式: 0 0 6,12,18 * * ?  # 每天6点、12点、18点执行
```

### 参数格式

```bash
# 格式: key1=value1;key2=value2;key3=value3

# 1. 默认参数（推荐）
""  # 空参数，使用自动模式，当前时间

# 2. 自动模式（根据时间智能选择）
"mode=AUTO"

# 3. 手动模式（执行所有配置）
"mode=MANUAL"

# 4. 指定时间执行
"mode=AUTO;time=2025-01-01T08:00:00"

# 5. 执行特定规则
"mode=MANUAL;rules=单条告警,整体告警"

# 6. 控制并行执行
"mode=AUTO;parallel=false"

# 7. 组合参数
"mode=AUTO;time=2025-01-01T14:00:00;parallel=true"
```

## ⏰ 调度策略

### 时间段划分

| 时间段 | 时间范围 | 执行内容 | 匹配规则 |
|--------|----------|----------|----------|
| **上午时段** | 6:00-12:00 | 前一天数据检查 | 备注包含"上午"、"前一天"、"am" |
| **下午时段** | 12:00-18:00 | 当天数据检查 | 备注包含"下午"、"当天"、"pm" |
| **日报时段** | 9:00-10:00 | 日报推送 | 备注包含"日报"、"每日" |

### 智能调度逻辑

```java
// 示例：当前时间为上午8点
// 系统会自动执行所有标记为"上午执行"的配置

if (当前时间在6:00-12:00) {
    执行包含"上午"、"前一天"、"am"的配置
}
if (当前时间在12:00-18:00) {
    执行包含"下午"、"当天"、"pm"的配置  
}
if (当前时间在9:00-10:00) {
    执行包含"日报"、"每日"的配置
}
```

## 🔧 配置示例

### 统一配置文件示例

```yaml
alert-rules:
  - name: 单条告警
    remark: 单条预警
    expr: "SELECT * FROM bi_measurement_general..."
    routes:
      - type: tcl-custom
    executions:
      - execKey: single_alert_morning_${.now?string('yyyy-MM-dd')}
        remark: 上午执行 - 检查前一天数据  # 🔑 关键字"上午"
        params:
          collectDate: ${(.now?long - 86400000)?number_to_date?string('yyyy-MM-dd')}
      - execKey: single_alert_afternoon_${.now?string('yyyy-MM-dd')}
        remark: 下午执行 - 检查当天数据   # 🔑 关键字"下午"
        params:
          collectDate: ${.now?string('yyyy-MM-dd')}

  - name: 飞书日报
    remark: 飞书日报推送
    expr: "SELECT * FROM bi_measurement_general..."
    routes:
      - type: feishu
    executions:
      - execKey: daily_report_${.now?string('yyyy-MM-dd')}
        remark: 每日报告推送              # 🔑 关键字"每日"
        params:
          collectDate: ${(.now?long - 86400000)?number_to_date?string('yyyy-MM-dd')}
```

### XXL-Job调度配置

```bash
# 方案1：定时自动执行（推荐）
Cron: 0 0 6,12,18 * * ?
参数: mode=AUTO

# 方案2：高频检查
Cron: 0 */30 * * * ?  # 每30分钟检查一次
参数: mode=AUTO

# 方案3：手动触发
Cron: 手动触发
参数: mode=MANUAL
```

## 📊 执行效果对比

### 原有方案 vs 单任务方案

| 方面 | 原有方案 | 单任务方案 | 改进效果 |
|------|----------|------------|----------|
| **XXL-Job任务数** | 6个任务 | 1个任务 | ✅ 减少83% |
| **调度复杂度** | 高（多任务协调） | 低（统一管理） | ✅ 大幅简化 |
| **时间管理** | 分散配置 | 智能判断 | ✅ 自动化 |
| **参数管理** | 复杂JSON | 简单字符串 | ✅ 简化90% |
| **监控难度** | 分散监控 | 集中监控 | ✅ 统一视图 |
| **故障排查** | 多点排查 | 单点排查 | ✅ 效率提升 |

### 执行流程对比

#### 原有流程
```
XXL-Job任务1 → 单条告警(上午) → 执行
XXL-Job任务2 → 单条告警(下午) → 执行  
XXL-Job任务3 → 整体告警(上午) → 执行
XXL-Job任务4 → 整体告警(下午) → 执行
XXL-Job任务5 → 账号汇总(上午) → 执行
XXL-Job任务6 → 账号汇总(下午) → 执行
```

#### 新流程
```
XXL-Job任务 → AlertScheduleManager → 智能判断时间 → 批量执行匹配的配置
```

## 🚀 优势总结

### 1. **管理简化**
- ✅ 从6个任务减少到1个任务
- ✅ 统一的参数格式
- ✅ 集中的配置管理

### 2. **智能调度**
- ✅ 自动时间判断
- ✅ 灵活的执行策略
- ✅ 支持手动干预

### 3. **运维友好**
- ✅ 统一监控入口
- ✅ 集中日志查看
- ✅ 简化故障排查

### 4. **扩展性强**
- ✅ 新增规则无需新建任务
- ✅ 支持动态配置调整
- ✅ 易于添加新的调度策略

## 🔧 实施步骤

### 步骤1：部署新组件
```bash
# 确保新的JobHandler已部署
AlertScheduleManager -> @JobHandler("alertScheduleManager")
```

### 步骤2：配置XXL-Job
```bash
# 创建新的XXL-Job任务
任务名称: 告警统一调度
JobHandler: alertScheduleManager
参数: mode=AUTO
Cron: 0 0 6,12,18 * * ?
```

### 步骤3：测试验证
```bash
# 手动触发测试
参数: mode=MANUAL

# 时间段测试
参数: mode=AUTO;time=2025-01-01T08:00:00
```

### 步骤4：逐步迁移
```bash
# 1. 新任务与旧任务并行运行
# 2. 验证执行结果一致性
# 3. 停用旧任务
# 4. 删除旧任务配置
```

## 📈 监控指标

### 关键指标
- **执行成功率**: 成功任务数 / 总任务数
- **执行时长**: 单次调度的总耗时
- **并发效率**: 并行执行 vs 串行执行的时间对比
- **规则覆盖率**: 实际执行的规则数 / 配置的规则数

### 监控方式
```java
// 执行结果示例
"执行完成 - 成功: 6, 失败: 0, 总计: 6"
"执行完成 - 成功: 5, 失败: 1, 总计: 6, 部分失败: 单条告警[0]: 数据库连接超时"
```

## 🎯 最佳实践

### 1. **参数配置**
```bash
# 生产环境推荐
mode=AUTO  # 使用自动模式

# 测试环境推荐  
mode=MANUAL  # 手动模式，执行所有配置
```

### 2. **时间配置**
```bash
# 推荐的Cron表达式
0 0 6,12,18 * * ?    # 每天3次，覆盖主要时段
0 */30 * * * ?       # 每30分钟，适合高频场景
```

### 3. **监控告警**
- 设置执行失败告警
- 监控执行时长异常
- 关注规则执行覆盖率

这个方案实现了您的需求：**只配置一个XXL-Job任务，就能管理所有的告警规则**，大大简化了调度配置和运维管理。
