# AlertScheduleManager 使用指南

## 🎯 快速开始

### 1. 基本使用

```bash
# XXL-Job配置
JobHandler: alertScheduleManager
参数: mode=AUTO
Cron: 0 0 6,12,18 * * ?
```

这样配置后，系统会：
- 每天6点执行"上午"相关的告警配置
- 每天12点执行"下午"相关的告警配置  
- 每天18点执行"下午"相关的告警配置
- 9-10点之间执行"日报"相关的配置

### 2. 参数详解

#### 执行模式参数
```bash
mode=AUTO    # 自动模式：根据当前时间智能选择执行配置
mode=MANUAL  # 手动模式：执行所有配置，忽略时间限制
```

#### 时间参数
```bash
# 指定执行时间（用于测试或补偿执行）
time=2025-01-01T08:00:00  # ISO格式时间
time=2025-01-01T14:30:00  # 下午2点30分
```

#### 规则筛选参数
```bash
# 只执行指定的规则
rules=单条告警,整体告警
rules=飞书日报
rules=单条告警,整体告警,账号汇总告警
```

#### 并行控制参数
```bash
parallel=true   # 并行执行（默认）
parallel=false  # 串行执行
```

## 📋 使用场景

### 场景1：正常定时执行
```bash
# XXL-Job配置
参数: mode=AUTO
Cron: 0 0 6,12,18 * * ?

# 执行效果：
# 6:00  → 执行所有"上午"配置
# 12:00 → 执行所有"下午"配置  
# 18:00 → 执行所有"下午"配置
```

### 场景2：补偿执行
```bash
# 手动触发，补偿昨天上午的数据
参数: mode=AUTO;time=2025-01-01T08:00:00

# 手动触发，执行所有配置
参数: mode=MANUAL
```

### 场景3：测试特定规则
```bash
# 只测试单条告警规则
参数: mode=MANUAL;rules=单条告警

# 测试多个规则
参数: mode=MANUAL;rules=单条告警,整体告警
```

### 场景4：故障恢复
```bash
# 串行执行，避免资源竞争
参数: mode=MANUAL;parallel=false

# 指定时间点的数据恢复
参数: mode=AUTO;time=2025-01-01T14:00:00;parallel=false
```

## ⏰ 时间策略详解

### 时间匹配规则

系统通过分析执行配置的`remark`字段来判断执行时间：

```yaml
executions:
  - execKey: "..."
    remark: "上午执行 - 检查前一天数据"  # ✅ 匹配上午时段
    params: {...}
  - execKey: "..."
    remark: "下午执行 - 检查当天数据"   # ✅ 匹配下午时段
    params: {...}
  - execKey: "..."
    remark: "每日报告推送"              # ✅ 匹配日报时段
    params: {...}
```

### 关键字匹配表

| 时间段 | 匹配关键字 | 示例备注 |
|--------|------------|----------|
| **上午时段** | 上午、前一天、am、AM | "上午执行"、"前一天数据检查" |
| **下午时段** | 下午、当天、pm、PM | "下午执行"、"当天数据分析" |
| **日报时段** | 日报、每日、daily、report | "每日报告"、"日报推送" |

### 时间段定义

```java
上午时段: 6:00 - 12:00  // 适合检查前一天的数据
下午时段: 12:00 - 18:00 // 适合检查当天的数据
日报时段: 9:00 - 10:00  // 适合推送日报
默认时段: 9:00 - 18:00  // 没有匹配关键字时的默认时间
```

## 🔧 配置最佳实践

### 1. 备注命名规范

```yaml
# ✅ 推荐的备注格式
remark: "上午执行 - 检查前一天数据"
remark: "下午执行 - 检查当天数据"  
remark: "每日报告推送"

# ❌ 不推荐的备注格式
remark: "执行配置1"
remark: "morning task"
remark: ""
```

### 2. XXL-Job调度配置

```bash
# 🎯 生产环境推荐配置
JobHandler: alertScheduleManager
参数: mode=AUTO
Cron: 0 0 6,12,18 * * ?
超时时间: 1800秒（30分钟）
失败重试次数: 1

# 🧪 测试环境配置
JobHandler: alertScheduleManager  
参数: mode=MANUAL
Cron: 手动触发
```

### 3. 监控配置

```bash
# 关键监控指标
- 执行成功率 > 95%
- 单次执行时长 < 30分钟
- 失败重试次数 < 3次

# 告警配置
- 执行失败立即告警
- 执行时长超过30分钟告警
- 连续3次失败告警
```

## 📊 执行结果解读

### 成功执行示例

```
执行完成 - 成功: 6, 失败: 0, 总计: 6
```

**解读**：
- 总共6个执行配置
- 全部执行成功
- 无失败情况

### 部分失败示例

```
执行完成 - 成功: 5, 失败: 1, 总计: 6, 部分失败: 单条告警[0]: 数据库连接超时
```

**解读**：
- 总共6个执行配置
- 5个成功，1个失败
- 失败原因：单条告警规则的第0个执行配置数据库连接超时

### 无任务执行示例

```
当前时间段没有需要执行的告警任务
```

**解读**：
- 当前时间不在任何配置的执行时间段内
- 或者没有匹配当前时间的执行配置

## 🐛 故障排查

### 常见问题

#### 1. 没有任务执行
```
问题: "当前时间段没有需要执行的告警任务"
原因: 执行配置的备注没有包含时间关键字
解决: 检查executions[].remark字段，确保包含"上午"、"下午"、"日报"等关键字
```

#### 2. 执行时间不对
```
问题: 上午时间执行了下午的配置
原因: 备注关键字配置错误
解决: 检查remark字段，确保关键字与期望的执行时间匹配
```

#### 3. 部分任务失败
```
问题: "部分失败: 单条告警[0]: 数据库连接超时"
原因: 具体执行配置遇到异常
解决: 查看详细日志，排查具体的执行配置问题
```

### 调试方法

#### 1. 手动测试
```bash
# 测试所有配置
参数: mode=MANUAL

# 测试特定时间
参数: mode=AUTO;time=2025-01-01T08:00:00

# 测试特定规则
参数: mode=MANUAL;rules=单条告警
```

#### 2. 日志分析
```bash
# 关键日志关键字
"开始执行告警调度管理"
"本次需要执行的任务数量"
"添加执行任务"
"任务执行成功"
"任务执行失败"
```

#### 3. 配置验证
```bash
# 运行配置验证测试
mvn test -Dtest=AlertScheduleManagerTest#testConfigurationValidation
```

## 🚀 高级用法

### 1. 动态参数调整

```bash
# 根据业务需要调整执行频率
# 高频监控（每15分钟）
Cron: 0 */15 * * * ?
参数: mode=AUTO

# 低频监控（每6小时）  
Cron: 0 0 */6 * * ?
参数: mode=AUTO
```

### 2. 分环境配置

```bash
# 开发环境：手动触发
参数: mode=MANUAL

# 测试环境：高频自动
参数: mode=AUTO
Cron: 0 */30 * * * ?

# 生产环境：标准自动
参数: mode=AUTO  
Cron: 0 0 6,12,18 * * ?
```

### 3. 业务定制

```bash
# 只在工作日执行
Cron: 0 0 6,12,18 * * 1-5
参数: mode=AUTO

# 周末特殊处理
Cron: 0 0 10 * * 6,0
参数: mode=MANUAL;rules=飞书日报
```

## 📈 性能优化

### 1. 并行执行优化

```bash
# 启用并行执行（默认）
参数: mode=AUTO;parallel=true

# 资源受限时使用串行
参数: mode=AUTO;parallel=false
```

### 2. 执行时间优化

```bash
# 错峰执行，避免集中在整点
Cron: 0 5 6,12,18 * * ?  # 6:05, 12:05, 18:05
```

### 3. 资源监控

- 监控CPU使用率
- 监控内存使用情况
- 监控数据库连接数
- 监控执行时长趋势

通过这个统一的调度管理器，您可以用一个XXL-Job任务轻松管理所有告警规则，实现智能化、自动化的告警调度。
